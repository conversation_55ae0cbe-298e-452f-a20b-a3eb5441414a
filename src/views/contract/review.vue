<template>
    <div class="flex h-full bg-white dark:bg-[#101014]">
        <!-- 左侧栏 -->
        <div :class="[
            'border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-300 bg-white dark:bg-[#101014]',
            isCollapsed ? 'w-0 overflow-hidden' : 'w-[20%] min-w-[280px]'
        ]">
            <!-- 新建任务按钮 -->
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <button
                    class="w-full px-4 py-3 rounded-lg text-white font-medium transition-all duration-300 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 gradient-button"
                    @click="handleCreateTask">
                    <div class="flex items-center justify-center space-x-2">
                        <NIcon size="18">
                            <AddOutline />
                        </NIcon>
                        <span>新建任务</span>
                    </div>
                </button>
            </div>

            <!-- 搜索框 -->
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <NInput v-model:value="searchValue" placeholder="搜索任务列表" clearable>
                    <template #prefix>
                        <NIcon>
                            <SearchOutline />
                        </NIcon>
                    </template>
                </NInput>
            </div>

            <!-- 任务列表 -->
            <div class="flex-1 overflow-y-auto">
                <div v-for="task in filteredTasks" :key="task.id"
                    class="p-4 border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer task-item"
                    :class="{ 'bg-blue-50 dark:bg-blue-900/20': selectedTask?.id === task.id }"
                    @click="selectTask(task)">
                    <div class="flex items-start space-x-3">
                        <!-- 文件类型图标 -->
                        <div class="flex-shrink-0 mt-1">
                            <NIcon size="20" :color="getFileTypeColor(task.fileType)">
                                <DocumentTextOutline v-if="task.fileType === 'doc'" />
                                <DocumentOutline v-else-if="task.fileType === 'pdf'" />
                                <DocumentTextOutline v-else />
                            </NIcon>
                        </div>
                        <!-- 任务信息 -->
                        <div class="flex-1 min-w-0">
                            <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                                {{ task.fileName }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                {{ formatDate(task.createDate) }}
                            </div>
                            <div class="mt-2">
                                <NBadge :value="getStatusText(task.status)" :type="getStatusType(task.status)" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 收起/展开按钮 -->
        <div class="flex flex-col justify-center">
            <button
                class="w-6 h-12 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 border-l border-r border-gray-200 dark:border-gray-700 flex items-center justify-center transition-colors"
                @click="toggleSidebar">
                <NIcon size="14">
                    <ChevronBackOutline v-if="!isCollapsed" />
                    <ChevronForwardOutline v-else />
                </NIcon>
            </button>
        </div>

        <!-- 右侧主界面 -->
        <div class="flex-1 flex flex-col h-full">
            <!-- 主标题区域 -->
            <div class="p-8 text-center">
                <h1
                    class="text-5xl font-bold mb-4 bg-gradient-to-r from-blue-500 via-purple-500 to-blue-600 bg-clip-text text-transparent">
                    智能诊断 一览无余
                </h1>
                <p class="text-xl text-gray-600 dark:text-gray-400 font-medium">
                    精准定位条款原文，提供专业的风险判断，风险分析及修改示例
                </p>
            </div>

            <!-- 选择器区域 -->
            <div class="px-8 mb-6">
                <div class="flex space-x-6">
                    <!-- 合同场景选择 -->
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            合同场景
                        </label>
                        <NSelect v-model:value="contractScenario" :options="contractScenarioOptions"
                            placeholder="请选择合同场景" class="w-full" />
                    </div>
                    <!-- 审查立场选择 -->
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            审查立场
                        </label>
                        <NSelect v-model:value="reviewStance" :options="reviewStanceOptions" placeholder="请选择审查立场"
                            class="w-full" />
                    </div>
                </div>
            </div>

            <!-- 上传文件区域 -->
            <div class="px-8 mb-8">
                <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-12 text-center cursor-pointer upload-area"
                    @click="handleUploadClick" @dragover.prevent @drop.prevent="handleFileDrop">
                    <div class="flex flex-col items-center">
                        <NIcon size="48" class="text-gray-400 dark:text-gray-500 mb-4">
                            <CloudUploadOutline />
                        </NIcon>
                        <div class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                            将文件拖拽到此处或<span class="text-blue-500 cursor-pointer">点击上传</span>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            文档格式：DOC/DOCX/PDF，单次最多上传10份，单份大小10M内
                        </div>
                    </div>
                </div>

                <!-- 已上传文件列表 -->
                <div v-if="uploadedFiles.length > 0" class="mt-4">
                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        已上传文件 ({{ uploadedFiles.length }}/10)
                    </div>
                    <div class="space-y-2">
                        <div v-for="(file, index) in uploadedFiles" :key="index"
                            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <NIcon size="20" :color="getFileTypeColor(getFileExtension(file.name))">
                                    <DocumentTextOutline v-if="getFileExtension(file.name) === 'doc'" />
                                    <DocumentOutline v-else />
                                </NIcon>
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ file.name }}
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ formatFileSize(file.size) }}
                                    </div>
                                </div>
                            </div>
                            <NButton size="small" type="error" ghost @click="removeFile(index)">
                                删除
                            </NButton>
                        </div>
                    </div>
                </div>

                <!-- 隐藏的文件输入 -->
                <input ref="fileInput" type="file" multiple accept=".doc,.docx,.pdf" class="hidden"
                    @change="handleFileSelect" />
            </div>

            <!-- 按钮区域 -->
            <div class="px-8 pb-8">
                <div class="flex space-x-4 justify-center">
                    <button
                        class="px-8 py-3 rounded-lg text-white font-medium transition-all duration-300 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 gradient-button"
                        @click="handleStartReview">
                        开始合同审查
                    </button>
                    <NButton type="default" size="large" @click="handleManageChecklist">
                        审核清单管理
                    </NButton>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    AddOutline,
    SearchOutline,
    DocumentTextOutline,
    DocumentOutline,
    ChevronBackOutline,
    ChevronForwardOutline,
    CloudUploadOutline,
} from "@vicons/ionicons5";
import {
    NIcon,
    NInput,
    NSelect,
    NButton,
    NBadge,
    useMessage,
} from "naive-ui";
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";

const message = useMessage();
const router = useRouter();
const route = useRoute();

// 左侧栏状态
const isCollapsed = ref(false);
const searchValue = ref("");

// 选择器数据
const contractScenario = ref("");
const reviewStance = ref("");

// 文件上传
const fileInput = ref<HTMLInputElement>();
const uploadedFiles = ref<File[]>([]);

// 任务列表
const selectedTask = ref<any>(null);

// 模拟任务数据
const tasks = ref([
    {
        id: 1,
        fileName: "租赁合同_2024.docx",
        fileType: "doc",
        createDate: new Date("2024-01-15"),
        status: "completed"
    },
    {
        id: 2,
        fileName: "销售协议_V2.pdf",
        fileType: "pdf",
        createDate: new Date("2024-01-14"),
        status: "processing"
    },
    {
        id: 3,
        fileName: "服务合同_草稿.doc",
        fileType: "doc",
        createDate: new Date("2024-01-13"),
        status: "pending"
    }
]);

// 合同场景选项
const contractScenarioOptions = [
    { label: "租赁合同", value: "lease" },
    { label: "销售合同", value: "sales" },
    { label: "服务合同", value: "service" },
    { label: "采购合同", value: "procurement" },
    { label: "劳动合同", value: "labor" },
];

// 审查立场选项
const reviewStanceOptions = [
    { label: "甲方立场", value: "party_a" },
    { label: "乙方立场", value: "party_b" },
    { label: "中立立场", value: "neutral" },
];

// 过滤后的任务列表
const filteredTasks = computed(() => {
    if (!searchValue.value) return tasks.value;
    return tasks.value.filter(task =>
        task.fileName.toLowerCase().includes(searchValue.value.toLowerCase())
    );
});

// 获取文件类型颜色
const getFileTypeColor = (fileType: string) => {
    switch (fileType) {
        case "doc":
            return "#1890ff";
        case "pdf":
            return "#f5222d";
        default:
            return "#666666";
    }
};

// 获取状态文本
const getStatusText = (status: string) => {
    switch (status) {
        case "completed":
            return "已完成";
        case "processing":
            return "处理中";
        case "pending":
            return "待处理";
        default:
            return "未知";
    }
};

// 获取状态类型
const getStatusType = (status: string) => {
    switch (status) {
        case "completed":
            return "success";
        case "processing":
            return "warning";
        case "pending":
            return "info";
        default:
            return "default";
    }
};

// 格式化日期
const formatDate = (date: Date) => {
    return date.toLocaleDateString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
    });
};

// 切换侧边栏
const toggleSidebar = () => {
    isCollapsed.value = !isCollapsed.value;
};

// 选择任务
const selectTask = (task: any) => {
    selectedTask.value = task;
};

// 新建任务
const handleCreateTask = () => {
    // 清空选择器和上传文件
    contractScenario.value = "";
    reviewStance.value = "";
    uploadedFiles.value = [];
    selectedTask.value = null;
    message.info("已清空当前数据，可以开始新建任务");
};

// 处理文件上传点击
const handleUploadClick = () => {
    fileInput.value?.click();
};

// 处理文件选择
const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files) {
        handleFiles(Array.from(target.files));
    }
};

// 处理文件拖拽
const handleFileDrop = (event: DragEvent) => {
    if (event.dataTransfer?.files) {
        handleFiles(Array.from(event.dataTransfer.files));
    }
};

// 处理文件
const handleFiles = (files: File[]) => {
    // 验证文件类型和大小
    const validFiles = files.filter(file => {
        const validTypes = ['.doc', '.docx', '.pdf'];
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
        const isValidType = validTypes.includes(fileExtension);
        const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB

        if (!isValidType) {
            message.error(`文件 ${file.name} 格式不支持`);
            return false;
        }
        if (!isValidSize) {
            message.error(`文件 ${file.name} 大小超过10MB`);
            return false;
        }
        return true;
    });

    if (uploadedFiles.value.length + validFiles.length > 10) {
        message.error("最多只能上传10个文件");
        return;
    }

    uploadedFiles.value.push(...validFiles);
    message.success(`成功添加 ${validFiles.length} 个文件`);
};

// 获取文件扩展名
const getFileExtension = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (extension === 'docx' || extension === 'doc') {
        return 'doc';
    }
    return extension || '';
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 删除文件
const removeFile = (index: number) => {
    uploadedFiles.value.splice(index, 1);
    message.success("文件删除成功");
};

// 开始合同审查
const handleStartReview = () => {
    if (!contractScenario.value) {
        message.error("请选择合同场景");
        return;
    }
    if (!reviewStance.value) {
        message.error("请选择审查立场");
        return;
    }
    if (uploadedFiles.value.length === 0) {
        message.error("请上传合同文件");
        return;
    }

    message.success("开始合同审查...");
    // 这里可以添加实际的审查逻辑
};

// 审核清单管理
const handleManageChecklist = () => {
    router.push("/contract/index");
};

// 组件挂载时检查路由参数
onMounted(() => {
    // 如果从合同清单页面跳转过来，可以获取传递的参数
    if (route.query.scenario) {
        contractScenario.value = route.query.scenario as string;
    }
    if (route.query.stance) {
        reviewStance.value = route.query.stance as string;
    }
});
</script>

<style scoped>
/* 自定义样式 */
.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

/* 主标题动画效果 */
h1 {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/* 按钮悬停效果增强 */
.gradient-button {
    background-size: 200% 200%;
    animation: gradient-button 3s ease infinite;
    box-shadow: 0 4px 15px 0 rgba(59, 130, 246, 0.3);
}

.gradient-button:hover {
    box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.5);
    transform: translateY(-2px);
}

@keyframes gradient-button {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/* 文件上传区域悬停效果 */
.upload-area {
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.15);
}

/* 任务列表项悬停效果 */
.task-item {
    transition: all 0.2s ease;
}

.task-item:hover {
    transform: translateX(4px);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
